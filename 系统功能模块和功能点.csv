功能模块,业务领域,功能点,功能描述,业务价值
流量解析和规则引擎,数据输入与预处理业务域,网络数据包捕获,实时从网络接口捕获数据包，支持DPDK高性能捕获和多网卡并行处理,为流量分析提供实时数据源，是整个探针系统的数据入口
,,离线数据包读取,从PCAP文件中读取历史流量数据，支持文件修复和分段分析,支持历史流量回放分析，满足事后调查和离线分析需求
,,数据包解析与验证,解析数据包头部信息，验证数据包完整性和格式正确性,确保后续分析基于有效数据，提供标准化的数据包结构
,协议识别与解析业务域,基础网络协议解析,解析以太网、IP、TCP/UDP、ICMP等网络协议栈基础层,提供网络通信的基础信息，为上层分析提供协议字段数据
,,应用层协议识别,自动识别HTTP、DNS、SSH、SSL、FTP等应用层协议类型,准确识别应用类型，为针对性分析和检测提供基础
,,协议深度解析,深度解析协议字段内容，提取URL、域名、用户代理等关键信息,提供丰富的应用层语义信息，支持内容级别的安全分析
,,协议指纹识别,基于协议特征进行应用指纹、操作系统指纹、设备指纹识别,识别网络中的资产类型和版本，支持资产管理和漏洞评估
,网络会话管理业务域,会话创建与关联,根据五元组创建网络会话，关联相关数据包到对应会话,提供会话级别的分析视角，支持连接行为和通信模式分析
,,会话状态跟踪,跟踪TCP连接状态变化、会话生命周期和数据传输状态,监控连接质量和异常，识别连接劫持、重置攻击等威胁
,,会话超时管理,处理会话超时检测、资源回收和内存清理,确保系统资源有效利用，维护会话数据的准确性
,威胁检测与安全分析业务域,规则引擎,加载和执行各种安全检测规则，支持动态规则更新和匹配,提供灵活的威胁检测能力，支持自定义安全策略
,,威胁特征匹配,基于威胁情报和特征库进行恶意流量检测和匹配,识别已知威胁和攻击模式，提供准确的安全告警
,,异常行为检测,检测网络扫描、DDoS攻击、异常连接模式等可疑行为,发现未知威胁和异常活动，提供主动防护能力
,,白名单/黑名单处理,处理信任和阻断策略，支持重点目标识别和例外处理,减少误报，提高检测精度，支持差异化安全策略
,数据流分析与重组业务域,TCP流重组,组TCP分片和乱序数据包，还原完整的应用层数据流,支持应用层内容分析，确保分析的完整性和准确性
,,应用数据提取,从重组的数据流中提取文件、邮件、表单等应用数据,支持数据泄露检测和内容审计，提供证据收集能力
,,流量统计分析,实时统计流量大小、协议分布、连接数、带宽利用率等指标,提供网络运行状态监控，支持容量规划和性能优化
,,网络行为分析,分析通信模式、访问行为、时间规律等网络行为特征,识别异常行为模式，支持用户行为分析和风险评估
,内容分析与识别业务域,加密流量分析,分析SSL/TLS加密流量的元数据、证书信息和行为特征,在不解密的前提下识别加密流量中的威胁和异常
,地理位置与资产识别业务域,IP地理位置解析,基于GeoIP数据库解析IP地址的国家、城市等地理位置信息,支持地理位置相关的安全策略和合规要求
,,网络资产识别,识别网络中的服务器、终端设备类型、操作系统和服务版本,构建网络资产清单，支持资产管理和安全评估
,,域名解析与关联,解析域名信息，建立IP与域名的关联关系和历史记录,支持域名信誉分析和恶意域名检测
,数据输出与存储业务域,实时告警输出,基于检测到的威胁和异常实时输出告警信息,提供及时的安全响应能力，支持事件处置
,,结构化数据输出,将分析结果格式化为Protobuf结构化数据输出,支持数据集成和二次分析，提供标准化数据接口
,,PCAP数据留存,根据业务规则选择性保存原始数据包为PCAP文件,支持事后调查和证据保全，满足合规要求
,流量过滤与控制业务域,流量过滤,基于BPF和自定义规则过滤不需要分析的流量,提高分析效率，减少无关数据处理
,,流量限速控制,控制流量处理速率，防止系统过载和数据丢失,确保系统稳定运行，保证分析质量
,,数据包采样,在高流量场景下进行智能采样分析,在资源受限情况下保持检测能力，支持大流量环境
,业务配置与策略管理业务域,检测策略配置,管理各种检测策略、规则配置和分析参数设置,支持灵活的安全策略定制，适应不同业务需求
,,任务调度管理,支持多任务并行处理，任务优先级和资源分配管理,提高系统处理能力，支持多租户和多场景应用
,,业务规则更新,支持检测规则、威胁情报、特征库的动态更新和加载,保持检测能力的时效性，快速响应新威胁
数据处理与ETL,流量数据采集领域,流数据接入,统一处理Kafka数据源，支持多Topic并行消费,为平台提供高吞吐量的实时数据源，支持大规模网络环境的流量分析需求，确保数据采集的实时性和可靠性
,,Protobuf数据解析,使用统一的ZMPNMsg.JKNmsg消息格式，提供标准化的数据解析,确保数据格式的一致性和解析的准确性，为后续处理提供标准化的数据结构
,,数据流分发处理,基于消息类型(connect、http、dns、ssl等)进行数据流分发,提高数据处理效率，支持并行处理不同类型的网络协议数据。
,,数据质量控制,集成统一的数据质量检查，验证Protobuf消息完整性,确保进入系统的数据质量，减少后续分析的错误率，提高分析结果的可信度。
,协议元数据提取领域,DNS元数据提取,从ZMPNMsg.dns_msg中提取查询名称、查询类型、响应码、解析地址、TTL等详细信息,提取DNS查询和响应的详细信息，支持域名解析行为分析、DNS隧道检测等安全分析
,,HTTP元数据提取,从ZMPNMsg.http_msg中提取URL、方法、状态码、User-Agent、请求头、响应头等信息,提取Web访问的详细元数据，支持Web行为分析、恶意URL检测、用户行为画像等功能
,,SSL/TLS元数据提取,提取证书SHA1/SHA256、JA3/JA3S指纹、加密套件、自签名标识等信息,提取加密通信的元数据特征，支持证书分析、加密流量指纹识别、恶意SSL检测
,,工控协议元数据提取,支持Modbus、IEC104、EIP等工控协议的元数据提取，包括事务ID、功能码、从站ID等,支持工业网络安全分析，检测工控系统的异常操作和潜在攻击
,,会话信息解析,处理TCP连接信息，提取五元组、连接状态、流量统计,提供网络连接的基础信息，支持会话分析、流量统计、异常连接检测等功能
,数仓构建领域,数据仓库转换,将Protobuf数据转换为Doris表结构的Row对象,构建标准化的数据仓库结构，支持OLAP分析和大数据量的统计查询
,,维度数据处理,统一处理域名、IP、URL、User-Agent等维度数据,构建标准化的维度数据，支持多维度的数据分析和报表生成
,,会话数据聚合（DWD层会话日志）,聚合多协议数据，形成完整的会话视图,提供完整的会话级别数据视图，支持会话行为分析和安全检测
,数据存储领域,分层数据仓库存储,采用Apache Doris分层存储架构，包括ODS(原始数据层)、DWD(数据仓库层)、DIM(维度数据层),构建企业级数据仓库架构，支持不同粒度的数据分析需求，提高查询性能和存储效率
,,实时数据写入,实时写入数仓和图数据库，支持批量写入和流式写入,确保数据的实时性，支持实时监控和告警功能，满足业务对数据时效性的要求
,,MinIO对象存储,使用MinIO存储证书文件等，支持分布式存储和扩展,
规则与任务管理,规则管理业务领域,检测规则（采集规则）管理,提供采集规则的增删改查以及同步和下发等操作,供灵活的规则管理能力，支持安全检测能力的持续优化
,,过滤规则管理,提供过滤规则的增删改查以及同步和下发等操作,
,,检测模型管理,提供检测模型查询和启停功能,
,实时分析任务管理,主从任务管理,提供主从任务配置、任务状态控制、任务切换等功能,提供灵活的任务管理机制，支持多场景并行分析，提高系统资源利用率，满足不同业务需求的同时进行
,,网口绑定与管理,提供网口任务绑定、动态网口配置、网口状态监控等功能,实现网络流量的精细化管理，支持按网口进行流量分析，提高网络监控的灵活性和针对性
,,任务状态监控,提供实时状态跟踪、任务健康检查、性能监控等功能,提供全面的任务监控能力，确保实时分析任务的稳定运行，及时发现和解决问题
,离线分析任务管理,PCAP文件导入,提供文件发现和验证、批量导入处理、服务实例调度和规则同步等功能,提供高效的历史流量数据导入能力，支持大规模网络流量的离线分析和取证调查
,,离线数据分析任务,提供任务创建和配置、批次管理等功能,提供深度的离线数据分析能力，支持复杂的安全分析和调查需求，为安全决策提供数据支撑
,,任务监控,批处理任务的状态监控、进度跟踪和异常处理,提供可靠的批处理任务调度能力，确保离线分析任务的高效执行和资源的合理利用
,下载和导出任务管理,数据下载任务,提供会话数据下载、PCAP文件下载和下载任务调度功能,提供数据导出能力，支持数据的离线分析、备份和共享，满足合规和审计需求
会话分析,会话管理业务领域,会话查询,支持按时间范围，会话和协议属性，标签查询会话列表,提供网络会话的深度分析能力，支持网络行为分析和安全检测
,,会话详情,单个会话的详细信息查询和分析,
,,会话统计,会话数量、持续时间、流量统计等基础指标,
,,会话聚合,按协议、证书、IP、域名等维度的会话聚合分析,
,,查询历史和模板,记录查询历史，并可将查询条件保存为模板,
,,会话标注,支持用户自行添加和修改会话标签,
网络关系图谱,实体管理领域,网络实体创建与维护,支持IP、域名、证书、应用服务、组织等实体的标准化创建和维护,构建完整的网络资产清单，为网络安全态势感知提供实体基础，支持资产管理和风险评估
,,实体属性管理,统一管理实体属性，包括创建时间、更新时间、备注信息、关联标签等,提供实体的详细信息和上下文，支持精确的实体识别和分类，为安全分析提供丰富的数据维度
,,实体生命周期管理,管理实体的完整生命周期，包括创建、更新、失效和清理,跟踪网络实体的活跃状态，支持历史分析和趋势识别，为威胁狩猎提供时间线索
,关系建模领域,基础关系建立,标准化关系建模，支持关系属性和权重管理,构建网络实体间的基础关联关系，为复杂的网络行为分析提供关系基础
,,协议关系建模,基于协议元数据建立更精确的协议关系，支持关系属性的详细记录,反映网络通信的协议层面关系，支持协议级别的安全分析和异常检测
,,安全关系标识,管理安全相关的实体关系,明确标识网络中的安全角色和关系，支持攻击链分析和威胁溯源
,图谱查询领域,实体查询,提供按类型和VID的实体查询,快速发现实体的直接关联关系，支持安全调查和威胁分析的起点查询
,,子图遍历（多跳路径查询）,支持实体间的关联路径发现,发现实体间的间接关联关系，支持复杂攻击链的识别和分析
,,探索分析查询,支持交互式的关系探索分析,支持交互式的图谱探索，允许分析师逐步深入了解网络关系的复杂性
,,属性查询,按实体和关系的属性进行查询,精确定位符合特定条件的实体和关系，提高分析效率和准确性
,关联分析领域,实体聚类分析,支持基于图算法的智能聚类,发现具有相似行为模式的实体群组，支持威胁组织识别和行为模式分析
,,关系强度分析,基于图算法计算实体间的关系权重和重要性评分,识别网络中的关键关系和重要节点，为安全防护重点提供指导
,,异常关系检测,集成机器学习算法检测偏离正常模式的异常关系,及时发现异常的网络关系，支持未知威胁的早期发现
,,影响力传播分析,基于图算法模拟威胁在网络中的传播过程,评估安全事件的潜在影响范围，为应急响应和风险控制提供决策支持
安全与系统管理,认证与授权业务领域,用户认证管理,提供本地用户认证和令牌管理,确保系统访问安全，防止未授权访问，提供灵活的认证方式适应不同部署环境
,,权限控制管理,提供角色权限管理、API权限控制和资源访问控制,实现最小权限原则，确保用户只能访问授权资源，提高系统安全性
,系统管理业务领域,系统控制管理,提供系统电源管理、用户密码管理、系统重置等功能,提供远程系统管理能力，降低运维成本，提高系统管理效率
,,磁盘管理,提供磁盘监控、磁盘操作（磁盘更新、重组、挂载）、存储操作（磁盘空间清理和数据迁移）和RAID管理,确保存储系统稳定运行，优化存储性能，预防数据丢失风险
,,网络配置管理,提供网络接口管理、IP配置管理、NTP时间同步和网络诊断等功能,确保网络配置正确，保障网络通信稳定，提供网络故障快速定位能力
告警与通知,告警管理业务领域,告警查询,按告警名称，级别，处理状态，攻击阶段等进行查询和统计,
,,告警处置,提供告警处置建议、告警状态管理和告警抑制等功能,提供完整的告警处理流程，支持威胁分析和安全响应
,,告警分析,提供指标统计、模式识别和报表生成等功能,
,通知服务业务领域,告警订阅,提供告警订阅配置，规则匹配等功能,实现个性化通知服务，提高通知的针对性和有效性
,,告警通知,支持邮件通知和消息队列推送,提供多样化的通知方式，确保重要信息及时传达
监控与运维,监控服务业务领域,系统监控,提供资源监控、性能指标监控、进程监控和服务监控等功能,提供全面的系统监控能力，及时发现系统异常和性能瓶颈
,,应用监控,提供JVM监控、应用性能监控、连接池监控和业务指标监控统计等功能,保障应用系统稳定运行，优化应用性能，提高用户体验
,,监控数据实时推送,基于WebSocket推送实现监控大屏功能,提供实时监控能力，支持快速响应和决策支持
,,系统告警,按用户设定的阈值和规则产生系统告警,
资产与知识管理,通用资产管理领域,资产列表查询,支持按各种属性条件查询资产列表，提供分页、排序、筛选功能,构建完整的资产清单，支持资产盘点和管理
,,资产详情查看,查看资产的详细信息，包括基础属性、关联信息、统计数据等,提供资产的全面视图，支持深度分析和评估
,,资产搜索,支持关键字搜索和高级搜索，可按多种条件组合筛选,快速定位目标资产，提高资产管理和分析效率
,,资产标签管理,支持用户自定义资产标签，包括添加、修改、删除标签，支持批量标签操作,实现资产的分类管理，支持个性化的资产组织和安全策略制定
,,资产备注管理,支持为资产添加和修改备注信息，记录资产相关的重要信息,提供资产上下文信息，支持团队协作和知识积累
,,资产导出,支持资产数据的导出功能，包括列表导出和详情导出,支持数据的离线分析、报告生成和第三方系统集成
,,资产显示配置管理,管理用户的资产列表显示配置，包括列显示、排序、筛选等个性化设置,提供个性化的资产管理界面，提高用户操作效率和使用体验
,,资产模糊搜索,支持基于关键字的资产模糊搜索，快速定位相关资产,提供便捷的资产查找功能，提高资产管理效率
,,资产批量操作,支持对多个资产进行批量标签添加、备注修改、导出等操作,提高资产管理效率，支持大规模资产的统一管理
,,资产关联查询,查询不同资产类型之间的关联关系，如IP与域名、证书与域名等关联,发现资产间的关联关系，支持关联分析和威胁溯源
,,资产统计分析,提供资产的统计分析功能，包括数量分布、趋势分析、标签分布等,了解资产分布情况，支持资产管理决策和规划
,,资产变更历史,记录和查询资产的变更历史，包括标签变更、备注修改等操作记录,提供资产管理的审计能力，支持变更追踪和合规要求
,证书管理业务领域,证书解析与验证,提供证书内容解析、证书链验证、有效性检查等功能,确保证书数据的准确性和有效性，支持证书安全分析
,,证书类型分析,按证书类型（自签名、CA签名、EV证书等）进行分类和统计分析,了解网络中的证书类型分布，支持证书安全策略制定
,,证书有效期管理,分析证书有效期分布，识别即将过期和已过期的证书，提供过期提醒,支持证书生命周期管理，预防证书过期导致的服务中断
,,证书颁发者分析,统计不同证书颁发者的分布和数量，分析CA使用情况,了解证书颁发者分布，支持证书信任策略制定
,,证书域名关联分析,分析证书与域名的关联关系，识别证书覆盖的域名范围,支持域名证书匹配分析，识别证书配置异常
,,证书安全评估,评估证书安全性，包括密钥长度、签名算法、证书链验证等,识别不安全的证书配置，支持SSL/TLS安全加固
,,证书合规性检查,检查证书是否符合行业标准和合规要求,确保证书配置符合安全标准，满足合规要求
,,证书文件存储管理,提供证书文件的存储、下载、批量操作等功能,支持证书文件的管理和离线分析需求
,IP管理业务领域,IP地理位置分析,分析IP资产的地理位置分布，提供地理位置统计和可视化,支持地理位置相关的安全策略和合规要求分析
,,IP威胁情报关联,关联外部威胁情报数据，标识恶意IP和可疑IP资产,提供威胁情报增强的IP分析能力，支持主动威胁检测
,,IP端口服务分析,统计IP资产的开放端口和运行服务，提供服务分布分析,识别网络服务暴露面，支持攻击面分析和安全加固
,,IP ASN归属分析,分析IP的ASN归属信息，提供组织级别的IP分布统计,了解IP的组织归属，支持网络资产的组织级管理
,域名管理业务领域,域名解析记录分析,分析域名的DNS解析记录，包括A、AAAA、CNAME、MX等记录类型,支持域名解析行为分析和DNS安全检测
,,域名威胁情报关联,关联外部威胁情报数据，标识恶意域名和可疑域名资产,提供威胁情报增强的域名分析能力，支持恶意域名检测
,,子域名发现与管理,统计域名的子域名分布和数量，提供子域名发现和管理,识别域名的完整攻击面，支持子域名安全监控
,,域名注册信息分析,分析域名的注册信息，包括注册商、注册时间、到期时间等,了解域名的注册背景，支持域名合规性分析
,指纹管理业务领域,指纹类型分析,统计不同类型指纹（JA3、JA3S、HTTP指纹等）的分布和数量,了解网络中的设备和应用分布，支持网络资产盘点
,,指纹设备识别,基于指纹信息识别设备类型、操作系统、应用版本等,支持网络设备的自动识别和分类，提高资产管理效率
,,指纹行为关联分析,分析指纹与会话、IP、域名等资产的关联关系,发现设备和应用的网络行为模式，支持异常检测和威胁分析
,URL管理业务领域,URL分类统计,按协议、域名、路径模式、内容类型等维度统计URL分布,了解Web访问模式和内容分布，支持Web行为分析
,,URL威胁情报关联,关联外部威胁情报数据，标识恶意URL和可疑URL资产,提供威胁情报增强的URL分析能力，支持恶意URL检测
,,URL访问行为分析,统计URL的访问频次、访问时间分布、状态码分布等信息,分析Web访问行为模式，支持异常访问检测和用户行为分析
,,URL内容分析,分析URL的内容类型、响应大小、响应时间等特征,了解Web内容特征，支持Web性能分析和内容监控
,组织管理业务领域,组织关联资产统计,统计组织关联的IP、域名、证书等资产数量和分布,了解组织的网络资产规模和分布，支持组织级别的安全评估
,,组织威胁情报关联,关联外部威胁情报数据，标识恶意组织和可疑组织资产,提供威胁情报增强的组织分析能力，支持APT组织识别
,,组织ASN分析,分析组织的ASN信息，包括IP段分布、地理位置分布等,了解组织的网络基础设施分布，支持网络拓扑分析
,操作系统管理业务领域,操作系统版本统计,统计不同操作系统类型和版本的分布和数量,了解网络中的操作系统分布，支持系统资产盘点和安全评估
,,操作系统漏洞关联,关联外部漏洞数据库，标识存在已知漏洞的操作系统资产,提供漏洞增强的系统分析能力，支持漏洞管理和安全加固
,,操作系统指纹识别,基于网络流量特征识别操作系统类型和版本,支持被动的操作系统识别，提高网络资产发现能力
,UA管理业务领域,UA解析与分类,解析User-Agent字符串，识别浏览器类型、版本、操作系统、设备类型等信息,支持客户端设备和应用的自动识别分类
,,UA设备类型统计,统计不同设备类型（桌面、移动、平板等）的分布和数量,了解网络中的设备类型分布，支持设备管理和用户行为分析
,,UA浏览器统计,统计不同浏览器类型和版本的分布和数量,了解网络中的浏览器使用情况，支持兼容性分析和安全评估
,,UA异常检测,检测异常或伪造的User-Agent字符串,识别可能的恶意行为或爬虫活动，支持安全防护
,,,,
,知识库管理业务领域,威胁情报管理,,
,,地理位置数据,,
,元数据管理业务领域,会话标签库管理,管理会话分析中使用的标签库，包括标签的增删改查、分类管理和标签模板,提供标准化的会话标签体系，支持会话分析的一致性和效率提升
,,资产标签库管理,管理各类资产标签库，包括IP、域名、URL、证书等资产标签的统一管理和维护,提供标准化的资产标签体系，支持资产分类管理和安全策略制定
,,资产显示配置管理,管理用户的资产列表显示配置，包括列显示、排序、筛选等个性化设置,提供个性化的资产管理界面，提高用户操作效率和使用体验
,,资产搜索模板管理,管理资产搜索的常用条件模板，支持搜索条件的保存和复用,提高资产搜索效率，支持复杂搜索条件的快速应用
,,资产分类规则管理,管理资产自动分类的规则配置，支持基于属性的自动标签分配,实现资产的智能分类，减少人工标注工作量，提高资产管理效率
